"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useData } from './DataContext'
import type { User } from '../types'

// Authentication types
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  name: string
  phone?: string
  agreeToTerms: boolean
  agreeToMarketing?: boolean
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export type AuthModalType = 'login' | 'register' | 'forgot-password' | null

export interface AuthContextType {
  // State
  authState: AuthState
  currentModal: AuthModalType
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  
  // Modal controls
  openModal: (type: AuthModalType) => void
  closeModal: () => void
  
  // Utility functions
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { currentUser, setCurrentUser, users } = useData()
  const [currentModal, setCurrentModal] = useState<AuthModalType>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Auth state derived from DataContext
  const authState: AuthState = {
    user: currentUser,
    isLoading,
    isAuthenticated: !!currentUser,
    error
  }

  // Clear error function
  const clearError = () => setError(null)

  // Login function
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // TODO: Replace with actual Supabase authentication
      // For now, simulate API call and find user by email
      await new Promise(resolve => setTimeout(resolve, 1000))

      const user = users.find(u => u.email === credentials.email)
      if (!user) {
        throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة')
      }

      // Simulate password check (in real app, this would be handled by Supabase)
      if (credentials.password.length < 6) {
        throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة')
      }

      setCurrentUser(user)
      setCurrentModal(null)
      
      // TODO: Store session in localStorage/cookies if rememberMe is true
      if (credentials.rememberMe) {
        localStorage.setItem('bentakon_remember_user', user.id)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدخول'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Register function
  const register = async (data: RegisterData): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // Validate passwords match
      if (data.password !== data.confirmPassword) {
        throw new Error('كلمات المرور غير متطابقة')
      }

      // Check if email already exists
      const existingUser = users.find(u => u.email === data.email)
      if (existingUser) {
        throw new Error('البريد الإلكتروني مستخدم بالفعل')
      }

      // TODO: Replace with actual Supabase registration
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Create new user
      const newUser: User = {
        id: `user_${Date.now()}`,
        email: data.email,
        name: data.name,
        role: 'user',
        walletBalance: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        phone: data.phone,
        isActive: true
      }

      // In a real app, this would be handled by Supabase
      // For now, we'll just set the current user
      setCurrentUser(newUser)
      setCurrentModal(null)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الحساب'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true)
      
      // TODO: Replace with actual Supabase logout
      await new Promise(resolve => setTimeout(resolve, 500))
      
      setCurrentUser(null)
      localStorage.removeItem('bentakon_remember_user')
      
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Reset password function
  const resetPassword = async (email: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // TODO: Replace with actual Supabase password reset
      await new Promise(resolve => setTimeout(resolve, 1000))

      // For now, just show success message
      // In real app, Supabase would send reset email
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Modal controls
  const openModal = (type: AuthModalType) => {
    clearError()
    setCurrentModal(type)
  }

  const closeModal = () => {
    clearError()
    setCurrentModal(null)
  }

  // Check for remembered user on mount
  useEffect(() => {
    const rememberedUserId = localStorage.getItem('bentakon_remember_user')
    if (rememberedUserId && !currentUser) {
      const user = users.find(u => u.id === rememberedUserId)
      if (user) {
        setCurrentUser(user)
      }
    }
  }, [users, currentUser, setCurrentUser])

  const value: AuthContextType = {
    authState,
    currentModal,
    login,
    register,
    logout,
    resetPassword,
    openModal,
    closeModal,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
